class GCashPaymentApp {
    constructor() {
        this.checkout = null;
        this.gcashComponent = null;
        this.init();
    }

    init() {
        this.setupEventListeners();
        this.updateTotal();
    }

    setupEventListeners() {
        // Form submission
        document.getElementById('orderForm').addEventListener('submit', (e) => {
            e.preventDefault();
            this.createPaymentSession();
        });

        // Add item button
        document.getElementById('addItem').addEventListener('click', () => {
            this.addItemRow();
        });

        // Update total when inputs change
        document.addEventListener('input', (e) => {
            if (e.target.classList.contains('item-quantity') || 
                e.target.classList.contains('item-price')) {
                this.updateTotal();
            }
        });

        // Remove item buttons
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('remove-item')) {
                this.removeItemRow(e.target);
            }
        });
    }

    addItemRow() {
        const itemsList = document.getElementById('itemsList');
        const itemRow = document.createElement('div');
        itemRow.className = 'item-row';
        itemRow.innerHTML = `
            <input type="text" placeholder="Item name" class="item-name" required>
            <input type="number" placeholder="Qty" class="item-quantity" value="1" min="1" required>
            <input type="number" placeholder="Price" class="item-price" value="0.00" step="0.01" min="0" required>
            <button type="button" class="remove-item">Remove</button>
        `;
        itemsList.appendChild(itemRow);
        this.updateTotal();
    }

    removeItemRow(button) {
        const itemRows = document.querySelectorAll('.item-row');
        if (itemRows.length > 1) {
            button.closest('.item-row').remove();
            this.updateTotal();
        } else {
            alert('You must have at least one item');
        }
    }

    updateTotal() {
        const itemRows = document.querySelectorAll('.item-row');
        let total = 0;
        
        itemRows.forEach(row => {
            const quantity = parseFloat(row.querySelector('.item-quantity').value) || 0;
            const price = parseFloat(row.querySelector('.item-price').value) || 0;
            total += quantity * price;
        });

        const currency = document.getElementById('currency').value;
        const currencySymbols = { PHP: '₱', HKD: 'HK$', SGD: 'S$' };
        const symbol = currencySymbols[currency] || currency;
        
        document.getElementById('totalAmount').textContent = `${symbol}${total.toFixed(2)}`;
    }

    getOrderData() {
        const itemRows = document.querySelectorAll('.item-row');
        const items = [];
        let totalAmount = 0;

        itemRows.forEach((row, index) => {
            const name = row.querySelector('.item-name').value.trim();
            const quantity = parseInt(row.querySelector('.item-quantity').value) || 1;
            const unitPrice = parseFloat(row.querySelector('.item-price').value) || 0;
            
            if (name && quantity > 0 && unitPrice > 0) {
                items.push({
                    reference: `item-${index + 1}`,
                    name: name,
                    quantity: quantity,
                    unit_price: unitPrice
                });
                totalAmount += quantity * unitPrice;
            }
        });

        return {
            amount: totalAmount,
            currency: document.getElementById('currency').value,
            reference: `ORDER-${Date.now()}`,
            customerName: document.getElementById('customerName').value.trim(),
            customerEmail: document.getElementById('customerEmail').value.trim(),
            items: items
        };
    }

    async createPaymentSession() {
        const createButton = document.getElementById('createPayment');
        const paymentSection = document.getElementById('paymentSection');
        const statusDiv = document.getElementById('paymentStatus');

        try {
            // Validate form
            const orderData = this.getOrderData();
            if (!orderData.customerName || !orderData.customerEmail || orderData.items.length === 0) {
                throw new Error('Please fill in all required fields');
            }

            // Show loading state
            createButton.disabled = true;
            createButton.textContent = 'Creating Payment Session...';
            statusDiv.innerHTML = '<div class="status-loading">Creating payment session...</div>';

            console.log('Creating payment session with data:', orderData);

            // Create payment session
            const response = await fetch('/api/payments/create-session', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(orderData)
            });

            const data = await response.json();

            if (!response.ok) {
                throw new Error(data.error || 'Failed to create payment session');
            }

            console.log('Payment session created:', data);

            // Initialize Checkout.com Flow
            await this.initializeCheckoutFlow(data.paymentSession, data.publicKey);

            // Show payment section
            paymentSection.style.display = 'block';
            paymentSection.scrollIntoView({ behavior: 'smooth' });

            statusDiv.innerHTML = '<div class="status-success">Payment session created! Please complete your payment below.</div>';

        } catch (error) {
            console.error('Error creating payment session:', error);
            statusDiv.innerHTML = `<div class="status-error">Error: ${error.message}</div>`;
        } finally {
            createButton.disabled = false;
            createButton.textContent = 'Create Payment Session';
        }
    }

    async initializeCheckoutFlow(paymentSession, publicKey) {
        try {
            console.log('Initializing Checkout.com Flow...');

            // Initialize CheckoutWebComponents
            this.checkout = await CheckoutWebComponents({
                paymentSession: paymentSession,
                publicKey: publicKey,
                environment: 'sandbox', // Change to 'production' for live
                appearance: {
                    theme: 'light',
                    variables: {
                        colorPrimary: '#0072ff',
                        colorBackground: '#ffffff',
                        colorText: '#333333',
                        borderRadius: '8px'
                    }
                }
            });

            console.log('CheckoutWebComponents initialized');

            // Create GCash component
            this.gcashComponent = this.checkout.create('gcash');

            console.log('GCash component created');

            // Check if GCash is available
            const isAvailable = await this.gcashComponent.isAvailable();
            console.log('GCash availability:', isAvailable);

            if (isAvailable) {
                // Mount the GCash component
                this.gcashComponent.mount('#gcash-container');
                console.log('GCash component mounted');

                // Set up event listeners
                this.setupPaymentEventListeners();
            } else {
                throw new Error('GCash payment method is not available');
            }

        } catch (error) {
            console.error('Error initializing Checkout Flow:', error);
            throw error;
        }
    }

    setupPaymentEventListeners() {
        if (!this.gcashComponent) return;

        // Listen for payment events
        this.gcashComponent.on('ready', () => {
            console.log('GCash component is ready');
            document.getElementById('paymentStatus').innerHTML =
                '<div class="status-success">GCash payment is ready. Click to proceed with payment.</div>';
        });

        this.gcashComponent.on('change', (event) => {
            console.log('GCash component change:', event);
        });

        this.gcashComponent.on('error', (error) => {
            console.error('GCash component error:', error);
            document.getElementById('paymentStatus').innerHTML =
                `<div class="status-error">Payment error: ${error.message || 'Unknown error'}</div>`;
        });

        this.gcashComponent.on('success', (result) => {
            console.log('Payment success:', result);
            document.getElementById('paymentStatus').innerHTML =
                '<div class="status-success">Payment initiated successfully! You will be redirected...</div>';
        });
    }
}

// Initialize the app when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    console.log('Initializing GCash Payment App...');
    new GCashPaymentApp();
});
