const axios = require('axios');

async function testPaymentMethods() {
    console.log('🧪 Testing Payment Methods...\n');

    const testData = {
        amount: 10.00,
        currency: 'PHP',
        reference: 'TEST-' + Date.now(),
        customerName: 'Test Customer',
        customerEmail: '<EMAIL>',
        items: [{
            reference: 'demo-item',
            name: 'Demo Product',
            quantity: 1,
            unit_price: 10.00
        }]
    };

    // Test GCash payment
    console.log('1️⃣ Testing GCash Payment...');
    try {
        const gcashResponse = await axios.post('http://localhost:3000/api/payments/create-payment', {
            ...testData,
            paymentMethod: 'gcash'
        });

        console.log('✅ GCash Payment Response:');
        console.log('📄 Payment ID:', gcashResponse.data.paymentId);
        console.log('📊 Status:', gcashResponse.data.status);
        console.log('🔗 Redirect URL:', gcashResponse.data.redirectUrl ? 'Available' : 'Not available');

    } catch (error) {
        console.error('❌ GCash Payment Error:');
        if (error.response) {
            console.error('Status:', error.response.status);
            console.error('Error:', error.response.data.error);
            console.error('Details:', error.response.data.details?.error_codes || 'No details');
        } else {
            console.error('Error:', error.message);
        }
    }

    console.log('\n2️⃣ Testing Card Payment...');
    try {
        const cardResponse = await axios.post('http://localhost:3000/api/payments/create-payment', {
            ...testData,
            paymentMethod: 'card'
        });

        console.log('✅ Card Payment Response:');
        console.log('📄 Payment Session ID:', cardResponse.data.paymentSession?.id || 'Not available');
        console.log('🔑 Public Key:', cardResponse.data.publicKey ? 'Available' : 'Not available');
        console.log('🎯 Use Flow:', cardResponse.data.useFlow);

    } catch (error) {
        console.error('❌ Card Payment Error:');
        if (error.response) {
            console.error('Status:', error.response.status);
            console.error('Error:', error.response.data.error);
            console.error('Details:', error.response.data.details?.error_codes || 'No details');
        } else {
            console.error('Error:', error.message);
        }
    }
}

testPaymentMethods();
