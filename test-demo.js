const axios = require('axios');

async function testDemo() {
    console.log('🧪 Testing Demo Payment (Card-based)...\n');
    
    try {
        const response = await axios.post('http://localhost:3000/api/demo/create-demo-payment', {
            amount: 10.00,
            currency: 'PHP',
            customerName: 'Test Customer',
            customerEmail: '<EMAIL>',
            items: [{
                reference: 'demo-item',
                name: 'Demo Product',
                quantity: 1,
                unit_price: 10.00
            }]
        });
        
        console.log('✅ Demo Payment Success!');
        console.log('📄 Payment ID:', response.data.paymentId);
        console.log('📊 Status:', response.data.status);
        console.log('💬 Message:', response.data.message);
        
    } catch (error) {
        console.error('❌ Demo Payment Error:');
        if (error.response) {
            console.error('Status:', error.response.status);
            console.error('Data:', error.response.data);
        } else {
            console.error('Error:', error.message);
        }
    }
}

testDemo();
