<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Payment Success - GCash Demo</title>
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <div class="container">
        <header>
            <h1>✅ Payment Successful!</h1>
            <p>Your GCash payment has been processed</p>
        </header>

        <div class="payment-form">
            <div class="success-content">
                <div class="success-icon">🎉</div>
                <h2>Thank you for your payment!</h2>
                <p>Your transaction has been completed successfully.</p>
                
                <div id="paymentDetails" class="payment-details">
                    <h3>Payment Details</h3>
                    <div class="detail-row">
                        <span class="label">Payment ID:</span>
                        <span class="value" id="paymentId">Loading...</span>
                    </div>
                    <div class="detail-row">
                        <span class="label">Status:</span>
                        <span class="value status-success">Successful</span>
                    </div>
                    <div class="detail-row">
                        <span class="label">Amount:</span>
                        <span class="value" id="amount">Loading...</span>
                    </div>
                    <div class="detail-row">
                        <span class="label">Currency:</span>
                        <span class="value" id="currency">Loading...</span>
                    </div>
                    <div class="detail-row">
                        <span class="label">Reference:</span>
                        <span class="value" id="reference">Loading...</span>
                    </div>
                    <div class="detail-row">
                        <span class="label">Date:</span>
                        <span class="value" id="processedOn">Loading...</span>
                    </div>
                </div>

                <div class="actions">
                    <button onclick="window.print()" class="secondary-button">Print Receipt</button>
                    <a href="/" class="pay-button">Make Another Payment</a>
                </div>
            </div>
        </div>

        <div class="info-section">
            <h3>📧 What's Next?</h3>
            <ul>
                <li>A confirmation email has been sent to your email address</li>
                <li>Your order will be processed within 1-2 business days</li>
                <li>You can track your order status in your account</li>
                <li>For support, contact our customer service team</li>
            </ul>
        </div>
    </div>

    <script>
        // Extract payment ID from URL parameters
        const urlParams = new URLSearchParams(window.location.search);
        const paymentId = urlParams.get('cko-payment-id');

        if (paymentId) {
            document.getElementById('paymentId').textContent = paymentId;
            
            // Fetch payment details
            fetchPaymentDetails(paymentId);
        } else {
            document.getElementById('paymentDetails').innerHTML = 
                '<p class="status-error">No payment ID found in URL</p>';
        }

        async function fetchPaymentDetails(paymentId) {
            try {
                const response = await fetch(`/api/payments/payment/${paymentId}`);
                const data = await response.json();

                if (data.success && data.payment) {
                    const payment = data.payment;
                    
                    // Update payment details
                    document.getElementById('amount').textContent = 
                        (payment.amount / 100).toFixed(2);
                    document.getElementById('currency').textContent = payment.currency;
                    document.getElementById('reference').textContent = 
                        payment.reference || 'N/A';
                    document.getElementById('processedOn').textContent = 
                        new Date(payment.processed_on).toLocaleString();

                    console.log('Payment details loaded:', payment);
                } else {
                    throw new Error('Failed to load payment details');
                }
            } catch (error) {
                console.error('Error fetching payment details:', error);
                document.getElementById('paymentDetails').innerHTML = 
                    '<p class="status-error">Unable to load payment details</p>';
            }
        }
    </script>

    <style>
        .success-content {
            text-align: center;
            padding: 40px 20px;
        }

        .success-icon {
            font-size: 4rem;
            margin-bottom: 20px;
        }

        .success-content h2 {
            color: #28a745;
            margin-bottom: 15px;
            font-size: 2rem;
        }

        .success-content p {
            color: #666;
            font-size: 1.1rem;
            margin-bottom: 30px;
        }

        .payment-details {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 25px;
            margin: 30px 0;
            text-align: left;
        }

        .payment-details h3 {
            margin-bottom: 20px;
            color: #333;
            text-align: center;
        }

        .detail-row {
            display: flex;
            justify-content: space-between;
            padding: 10px 0;
            border-bottom: 1px solid #e9ecef;
        }

        .detail-row:last-child {
            border-bottom: none;
        }

        .label {
            font-weight: 600;
            color: #495057;
        }

        .value {
            color: #212529;
            font-family: monospace;
        }

        .actions {
            display: flex;
            gap: 15px;
            justify-content: center;
            margin-top: 30px;
        }

        .secondary-button {
            background: #6c757d;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            font-size: 16px;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
        }

        .secondary-button:hover {
            background: #5a6268;
        }

        @media (max-width: 768px) {
            .actions {
                flex-direction: column;
            }
            
            .detail-row {
                flex-direction: column;
                gap: 5px;
            }
        }
    </style>
</body>
</html>
