# Deployment Guide

This guide covers how to deploy your GCash payment integration to various platforms.

## 🚀 Quick Start

1. **Set up your environment variables**
2. **Choose a deployment platform**
3. **Configure webhooks**
4. **Test in production**

## 🌐 Platform-Specific Deployment

### Heroku

1. **Install Heroku CLI**
   ```bash
   npm install -g heroku
   ```

2. **Create Heroku app**
   ```bash
   heroku create your-app-name
   ```

3. **Set environment variables**
   ```bash
   heroku config:set CHECKOUT_SECRET_KEY=sk_your_secret_key
   heroku config:set CHECKOUT_PUBLIC_KEY=pk_your_public_key
   heroku config:set CHECKOUT_ENVIRONMENT=production
   heroku config:set NODE_ENV=production
   ```

4. **Deploy**
   ```bash
   git add .
   git commit -m "Deploy GCash integration"
   git push heroku main
   ```

5. **Configure webhooks**
   - URL: `https://your-app-name.herokuapp.com/api/webhooks/checkout`

### Vercel

1. **Install Vercel CLI**
   ```bash
   npm install -g vercel
   ```

2. **Create vercel.json**
   ```json
   {
     "version": 2,
     "builds": [
       {
         "src": "server.js",
         "use": "@vercel/node"
       }
     ],
     "routes": [
       {
         "src": "/(.*)",
         "dest": "/server.js"
       }
     ]
   }
   ```

3. **Deploy**
   ```bash
   vercel --prod
   ```

4. **Set environment variables** in Vercel dashboard

### Railway

1. **Connect your GitHub repository**
2. **Set environment variables** in Railway dashboard
3. **Deploy automatically** on push

### DigitalOcean App Platform

1. **Create new app** from GitHub repository
2. **Configure environment variables**
3. **Set build and run commands**:
   - Build: `npm install`
   - Run: `npm start`

## 🔧 Environment Configuration

### Production Environment Variables

```env
# Required
CHECKOUT_SECRET_KEY=sk_your_production_secret_key
CHECKOUT_PUBLIC_KEY=pk_your_production_public_key
CHECKOUT_ENVIRONMENT=production
NODE_ENV=production

# Optional
PORT=3000
WEBHOOK_SECRET=your_webhook_secret
SUCCESS_URL=https://yourdomain.com/success
FAILURE_URL=https://yourdomain.com/failure
```

### Domain Configuration

Update these URLs in your deployment:

1. **Success URL**: `https://yourdomain.com/success`
2. **Failure URL**: `https://yourdomain.com/failure`
3. **Webhook URL**: `https://yourdomain.com/api/webhooks/checkout`

## 🔗 Webhook Setup

### 1. Configure in Checkout.com Dashboard

1. **Log in** to [Checkout.com Dashboard](https://dashboard.checkout.com)
2. **Navigate** to Settings > Webhooks
3. **Add new webhook**:
   - URL: `https://yourdomain.com/api/webhooks/checkout`
   - Events: Select all payment events
   - Secret: Generate and save in environment variables

### 2. Test Webhook Delivery

```bash
# Test webhook endpoint
curl -X POST https://yourdomain.com/api/webhooks/checkout \
  -H "Content-Type: application/json" \
  -d '{"type":"payment_approved","data":{"id":"test"}}'
```

## 🔒 Security Checklist

### SSL/HTTPS
- ✅ Enable HTTPS on your domain
- ✅ Use SSL certificates (Let's Encrypt recommended)
- ✅ Redirect HTTP to HTTPS

### Environment Variables
- ✅ Never commit `.env` files
- ✅ Use platform-specific environment variable management
- ✅ Rotate API keys regularly

### Content Security Policy
- ✅ Configure CSP headers (already included)
- ✅ Allow Checkout.com domains
- ✅ Restrict script sources

### API Security
- ✅ Validate webhook signatures
- ✅ Implement rate limiting
- ✅ Log security events

## 📊 Monitoring

### Health Checks

Add health check endpoint monitoring:

```javascript
// Already included in server.js
app.get('/health', (req, res) => {
  res.json({ 
    status: 'OK', 
    timestamp: new Date().toISOString(),
    environment: process.env.CHECKOUT_ENVIRONMENT
  });
});
```

### Logging

Implement structured logging:

```javascript
// Example logging setup
const winston = require('winston');

const logger = winston.createLogger({
  level: 'info',
  format: winston.format.json(),
  transports: [
    new winston.transports.File({ filename: 'error.log', level: 'error' }),
    new winston.transports.File({ filename: 'combined.log' })
  ]
});
```

### Error Tracking

Consider integrating:
- **Sentry** for error tracking
- **LogRocket** for session replay
- **DataDog** for application monitoring

## 🧪 Testing in Production

### 1. Smoke Tests

```bash
# Test main page
curl https://yourdomain.com/

# Test health endpoint
curl https://yourdomain.com/health

# Test API endpoint
curl -X POST https://yourdomain.com/api/payments/create-session \
  -H "Content-Type: application/json" \
  -d '{"amount":100,"currency":"PHP","reference":"TEST","customerEmail":"<EMAIL>","items":[{"name":"Test","quantity":1,"reference":"test","unit_price":100}]}'
```

### 2. Payment Flow Tests

1. **Complete successful payment**
2. **Test payment failure scenarios**
3. **Verify webhook delivery**
4. **Check success/failure page redirects**

### 3. Load Testing

Use tools like:
- **Artillery.io**
- **Apache Bench (ab)**
- **k6**

Example Artillery test:
```yaml
config:
  target: 'https://yourdomain.com'
  phases:
    - duration: 60
      arrivalRate: 10
scenarios:
  - name: "Payment flow"
    requests:
      - get:
          url: "/"
      - post:
          url: "/api/payments/create-session"
          json:
            amount: 100
            currency: "PHP"
            reference: "LOAD-TEST"
            customerEmail: "<EMAIL>"
            items:
              - name: "Test Product"
                quantity: 1
                reference: "test"
                unit_price: 100
```

## 🚨 Troubleshooting

### Common Deployment Issues

1. **Environment variables not set**
   - Check platform-specific environment variable configuration
   - Verify variable names match exactly

2. **CORS errors**
   - Update CORS origins for production domain
   - Check CSP headers

3. **Webhook delivery failures**
   - Verify webhook URL is publicly accessible
   - Check webhook endpoint returns 200 status
   - Review webhook logs in Checkout.com dashboard

4. **SSL certificate issues**
   - Ensure SSL is properly configured
   - Check certificate validity
   - Verify HTTPS redirects

### Debug Commands

```bash
# Check environment variables
heroku config

# View logs
heroku logs --tail

# Test webhook connectivity
curl -I https://yourdomain.com/api/webhooks/checkout
```

## 📈 Performance Optimization

### 1. Caching
- Implement Redis for session storage
- Cache static assets with CDN
- Use HTTP caching headers

### 2. Database Optimization
- Add database for payment logging
- Implement connection pooling
- Use read replicas for reporting

### 3. CDN Setup
- Use CloudFlare or AWS CloudFront
- Cache static assets (CSS, JS, images)
- Enable gzip compression

## 🔄 Continuous Deployment

### GitHub Actions Example

```yaml
name: Deploy to Production

on:
  push:
    branches: [ main ]

jobs:
  deploy:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v2
    
    - name: Setup Node.js
      uses: actions/setup-node@v2
      with:
        node-version: '16'
        
    - name: Install dependencies
      run: npm install
      
    - name: Run tests
      run: npm test
      
    - name: Deploy to Heroku
      uses: akhileshns/heroku-deploy@v3.12.12
      with:
        heroku_api_key: ${{secrets.HEROKU_API_KEY}}
        heroku_app_name: "your-app-name"
        heroku_email: "<EMAIL>"
```

## 📞 Support

If you encounter issues during deployment:

1. **Check the logs** for detailed error messages
2. **Review the documentation** for your chosen platform
3. **Test locally** before deploying
4. **Contact platform support** if needed

---

**Remember**: Always test thoroughly in a staging environment before deploying to production!
