# GCash Payment Integration with Checkout.com

A complete implementation of GCash payments using Checkout.com's Flow payment interface. This demo application shows how to integrate GCash payments into your web application with proper server-side and client-side handling.

## 🚀 Features

- **Complete GCash Integration**: Full payment flow using Checkout.com
- **Multi-Currency Support**: PHP, HKD, and SGD currencies
- **Responsive Design**: Works on desktop and mobile devices
- **Webhook Handling**: Proper server-side webhook processing
- **Error Handling**: Comprehensive error handling and user feedback
- **Security**: CSP headers and secure API key handling
- **Test Environment**: Sandbox environment for safe testing

## 📋 Prerequisites

Before you begin, ensure you have:

1. **Node.js** (version 14 or higher)
2. **npm** or **yarn** package manager
3. **Checkout.com Account**: [Get a test account](https://www.checkout.com/get-test-account)
4. **API Keys**: Public and Secret keys from Checkout.com Dashboard

## 🛠️ Installation

1. **Clone or download this project**
   ```bash
   git clone <your-repo-url>
   cd gcash-checkout-integration
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Set up environment variables**
   ```bash
   cp .env.example .env
   ```

4. **Configure your `.env` file**
   ```env
   # Checkout.com API Configuration
   CHECKOUT_SECRET_KEY=sk_sbox_your_secret_key_here
   CHECKOUT_PUBLIC_KEY=pk_sbox_your_public_key_here
   CHECKOUT_ENVIRONMENT=sandbox

   # Server Configuration
   PORT=3000
   NODE_ENV=development

   # Webhook Configuration
   WEBHOOK_SECRET=your_webhook_secret_here

   # Application URLs
   SUCCESS_URL=http://localhost:3000/success
   FAILURE_URL=http://localhost:3000/failure
   ```

## 🔑 Getting API Keys

1. **Sign up** for a [Checkout.com test account](https://www.checkout.com/get-test-account)
2. **Log in** to the [Dashboard](https://dashboard.sandbox.checkout.com)
3. **Navigate** to Settings > API Keys
4. **Create keys** with the following scopes:
   - **Public key**: `payment-sessions:pay` and `vault-tokenization`
   - **Secret key**: `payment-sessions`

## 🚀 Running the Application

1. **Start the development server**
   ```bash
   npm run dev
   ```
   Or for production:
   ```bash
   npm start
   ```

2. **Open your browser**
   ```
   http://localhost:3000
   ```

3. **Test the integration**
   - Fill in the order form
   - Use test customer details
   - Complete the GCash payment flow

## 🔗 Webhook Configuration

For production use, you'll need to configure webhooks:

1. **Set up a public endpoint** (use ngrok for local testing)
   ```bash
   ngrok http 3000
   ```

2. **Configure webhook in Checkout.com Dashboard**
   - URL: `https://your-domain.com/api/webhooks/checkout`
   - Events: `payment_approved`, `payment_declined`, `payment_pending`, `payment_canceled`

3. **Update your `.env` file** with the webhook secret

## 📱 Testing

### Test Data for GCash

Since this is a sandbox environment, you can use any test data:

- **Customer Email**: Any valid email format
- **Customer Name**: Any name
- **Amount**: Any positive amount
- **Currency**: PHP, HKD, or SGD

### Test Scenarios

1. **Successful Payment**: Complete the normal flow
2. **Failed Payment**: Test error handling
3. **Webhook Testing**: Monitor server logs for webhook events

## 🏗️ Project Structure

```
gcash-checkout-integration/
├── public/                 # Frontend files
│   ├── index.html         # Main payment page
│   ├── success.html       # Success page
│   ├── failure.html       # Failure page
│   ├── styles.css         # Styling
│   └── app.js            # Frontend JavaScript
├── routes/                # API routes
│   ├── payments.js       # Payment session handling
│   └── webhooks.js       # Webhook processing
├── server.js             # Express server
├── package.json          # Dependencies
├── .env.example          # Environment template
└── README.md            # This file
```

## 🔧 Configuration Options

### Environment Variables

| Variable | Description | Required |
|----------|-------------|----------|
| `CHECKOUT_SECRET_KEY` | Your Checkout.com secret key | Yes |
| `CHECKOUT_PUBLIC_KEY` | Your Checkout.com public key | Yes |
| `CHECKOUT_ENVIRONMENT` | `sandbox` or `production` | Yes |
| `PORT` | Server port (default: 3000) | No |
| `NODE_ENV` | Environment mode | No |
| `WEBHOOK_SECRET` | Webhook verification secret | No |
| `SUCCESS_URL` | Success redirect URL | No |
| `FAILURE_URL` | Failure redirect URL | No |

### Supported Currencies

- **PHP** - Philippine Peso (primary for GCash)
- **HKD** - Hong Kong Dollar
- **SGD** - Singapore Dollar

## 🚨 Security Considerations

1. **Never expose secret keys** in frontend code
2. **Use HTTPS** in production
3. **Validate webhook signatures** (implement webhook secret verification)
4. **Sanitize user inputs** before processing
5. **Use CSP headers** (already configured)

## 🐛 Troubleshooting

### Common Issues

1. **"GCash not available"**
   - Check billing country is set to "PH"
   - Verify currency is PHP, HKD, or SGD
   - Ensure API keys are correct

2. **Payment session creation fails**
   - Verify secret key is correct
   - Check required fields are provided
   - Review server logs for detailed errors

3. **Webhooks not received**
   - Ensure webhook URL is publicly accessible
   - Check webhook configuration in Dashboard
   - Verify endpoint returns 200 status

### Debug Mode

Enable detailed logging by setting:
```env
NODE_ENV=development
```

Check browser console and server logs for detailed information.

## 📚 API Reference

### Create Payment Session
```
POST /api/payments/create-session
```

**Request Body:**
```json
{
  "amount": 100.00,
  "currency": "PHP",
  "reference": "ORDER-123",
  "customerName": "John Doe",
  "customerEmail": "<EMAIL>",
  "items": [
    {
      "name": "Product Name",
      "quantity": 1,
      "reference": "ITEM-1",
      "unit_price": 100.00
    }
  ]
}
```

### Get Payment Details
```
GET /api/payments/payment/:paymentId
```

## 🔄 Going Live

To use this in production:

1. **Change environment** to `production` in `.env`
2. **Update API keys** to production keys
3. **Configure production webhooks**
4. **Set up proper domain** and SSL certificate
5. **Update success/failure URLs**
6. **Implement proper error logging**
7. **Add monitoring and alerting**

## 📞 Support

- **Checkout.com Documentation**: [https://www.checkout.com/docs](https://www.checkout.com/docs)
- **GCash Integration Guide**: [https://www.checkout.com/docs/payments/add-payment-methods/gcash/web](https://www.checkout.com/docs/payments/add-payment-methods/gcash/web)
- **API Reference**: [https://api-reference.checkout.com](https://api-reference.checkout.com)

## 📄 License

MIT License - feel free to use this code for your projects.

---

**Note**: This is a demo application for educational purposes. For production use, implement additional security measures, error handling, and monitoring as needed for your specific requirements.
