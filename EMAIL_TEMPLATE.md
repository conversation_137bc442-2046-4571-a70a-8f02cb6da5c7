# 📧 Email Template for Checkout.com Support

## 📬 **Send To:**
```
<EMAIL>
```

## 📝 **Subject Line:**
```
Request: Enable GCash Payment Method and Processing Channel Setup - Sandbox Account
```

## 💌 **Email Body:**

```
Dear Checkout.com Support Team,

I hope this email finds you well. I am writing to request assistance with enabling GCash payments for my sandbox account.

**Account Details:**
- Account Email: [YOUR_EMAIL_HERE]
- Environment: Sandbox
- Business Name: [YOUR_BUSINESS_NAME]
- Country: Philippines

**Current Issue:**
I am integrating GCash payments into my application using your API, but I'm encountering the following error when attempting to create payments:

Error: "processing_channel_id_required"
Error Code: request_invalid

**What I've Verified:**
✅ API keys are working correctly (authentication successful)
✅ Integration code is properly implemented
✅ Following your official GCash documentation
✅ Using correct endpoints and data structure

**Request:**
Could you please help me with the following:

1. **Enable GCash payment method** for my sandbox account
2. **Configure the required processing channel** for GCash payments
3. **Set up processing channel for card payments** as well (if not already configured)
4. **Provide any additional setup instructions** or requirements

**Business Context:**
I am developing a payment solution for the Philippines market and need to support GCash as it's the primary digital wallet used by Filipino consumers. This integration is critical for our business operations in the region.

**Technical Details:**
- Using Direct Payments API (/payments endpoint)
- Currency: PHP (Philippine Peso)
- Payment Type: Regular
- Terminal Type: WEB

I would greatly appreciate your assistance in resolving this matter. Please let me know if you need any additional information or documentation from my side.

Thank you for your time and support. I look forward to your prompt response.

Best regards,
[YOUR_NAME]
[YOUR_TITLE]
[YOUR_COMPANY]
[YOUR_PHONE_NUMBER]
[YOUR_EMAIL]
```

## 🎯 **Key Points This Email Covers:**

1. **Clear Subject** - Immediately tells them what you need
2. **Professional Tone** - Shows you're a serious business user
3. **Specific Error** - Gives them exact technical details
4. **Proof of Competence** - Shows you've done your homework
5. **Business Context** - Explains why this matters
6. **Clear Request** - Lists exactly what you need
7. **Complete Information** - Provides all relevant details

## ⚡ **Why This Works:**

- **Specific**: Mentions exact error codes and technical details
- **Professional**: Uses business language and proper formatting
- **Complete**: Includes all information they'll need
- **Urgent but Polite**: Shows importance without being demanding
- **Technical**: Demonstrates you understand the integration

## 📞 **Alternative Contact Methods:**

If email is slow, you can also try:
- **Live Chat**: Check their website for chat support
- **Phone Support**: Look for their support phone number
- **Account Manager**: If you have one assigned

## ⏰ **Expected Response Time:**
- **Email Response**: 1-2 business days
- **Account Activation**: Same day after they process your request
- **Follow-up**: If no response in 3 days, send a polite follow-up

Copy this template, fill in your details, and send it right away! 🚀
