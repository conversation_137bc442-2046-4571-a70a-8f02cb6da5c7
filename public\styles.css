* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Se<PERSON>e UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    padding: 20px;
}

.container {
    max-width: 800px;
    margin: 0 auto;
    background: white;
    border-radius: 12px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

header {
    background: linear-gradient(135deg, #00c6ff 0%, #0072ff 100%);
    color: white;
    padding: 30px;
    text-align: center;
}

header h1 {
    font-size: 2.5rem;
    margin-bottom: 10px;
}

header p {
    font-size: 1.1rem;
    opacity: 0.9;
}

.payment-form, .payment-section, .info-section {
    padding: 30px;
}

.form-group {
    margin-bottom: 20px;
}

label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    color: #333;
}

input, select {
    width: 100%;
    padding: 12px;
    border: 2px solid #e1e5e9;
    border-radius: 8px;
    font-size: 16px;
    transition: border-color 0.3s ease;
}

input:focus, select:focus {
    outline: none;
    border-color: #0072ff;
}

.items-section {
    border: 2px solid #f0f0f0;
    border-radius: 8px;
    padding: 20px;
    margin: 20px 0;
}

.items-section h3 {
    margin-bottom: 15px;
    color: #333;
}

.item-row {
    display: grid;
    grid-template-columns: 2fr 1fr 1fr auto;
    gap: 10px;
    margin-bottom: 10px;
    align-items: center;
}

.item-row input {
    margin-bottom: 0;
}

.remove-item {
    background: #ff4757;
    color: white;
    border: none;
    padding: 8px 12px;
    border-radius: 6px;
    cursor: pointer;
    font-size: 14px;
}

.remove-item:hover {
    background: #ff3742;
}

#addItem {
    background: #2ed573;
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 6px;
    cursor: pointer;
    font-size: 14px;
    margin-top: 10px;
}

#addItem:hover {
    background: #26d065;
}

.total-section {
    background: #f8f9fa;
    padding: 20px;
    border-radius: 8px;
    margin: 20px 0;
    text-align: center;
}

.total-section h3 {
    font-size: 1.5rem;
    color: #333;
}

.pay-button {
    width: 100%;
    background: linear-gradient(135deg, #00c6ff 0%, #0072ff 100%);
    color: white;
    border: none;
    padding: 15px;
    border-radius: 8px;
    font-size: 18px;
    font-weight: 600;
    cursor: pointer;
    transition: transform 0.2s ease;
}

.pay-button:hover {
    transform: translateY(-2px);
}

.pay-button:disabled {
    background: #ccc;
    cursor: not-allowed;
    transform: none;
}

.payment-section {
    border-top: 2px solid #f0f0f0;
    background: #f8f9fa;
}

.payment-section h2 {
    margin-bottom: 20px;
    color: #333;
}

#gcash-container {
    min-height: 200px;
    border: 2px dashed #ddd;
    border-radius: 8px;
    padding: 20px;
    background: white;
    margin-bottom: 20px;
}

#paymentStatus {
    padding: 15px;
    border-radius: 8px;
    margin-top: 15px;
    font-weight: 600;
}

.status-success {
    background: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.status-error {
    background: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

.status-loading {
    background: #d1ecf1;
    color: #0c5460;
    border: 1px solid #bee5eb;
}

.info-section {
    background: #fff3cd;
    border-top: 2px solid #ffeaa7;
}

.info-section h3 {
    margin-bottom: 15px;
    color: #856404;
}

.info-section ul {
    list-style-position: inside;
    color: #856404;
}

.info-section li {
    margin-bottom: 8px;
}

@media (max-width: 768px) {
    .container {
        margin: 10px;
        border-radius: 8px;
    }
    
    header {
        padding: 20px;
    }
    
    header h1 {
        font-size: 2rem;
    }
    
    .payment-form, .payment-section, .info-section {
        padding: 20px;
    }
    
    .item-row {
        grid-template-columns: 1fr;
        gap: 5px;
    }
    
    .remove-item {
        justify-self: start;
        margin-top: 5px;
    }
}
