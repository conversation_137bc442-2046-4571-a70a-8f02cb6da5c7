const axios = require('axios');
require('dotenv').config();

async function testAPIKeys() {
    console.log('🔑 Testing Checkout.com API Keys...\n');
    
    const secretKey = process.env.CHECKOUT_SECRET_KEY;
    const publicKey = process.env.CHECKOUT_PUBLIC_KEY;
    
    console.log('Secret Key:', secretKey ? `${secretKey.substring(0, 15)}...` : 'NOT SET');
    console.log('Public Key:', publicKey ? `${publicKey.substring(0, 15)}...` : 'NOT SET');
    console.log('Environment:', process.env.CHECKOUT_ENVIRONMENT);
    console.log('');
    
    if (!secretKey || !publicKey) {
        console.error('❌ API keys are missing!');
        return;
    }
    
    // Test secret key by trying to get account info (simpler test)
    try {
        console.log('🧪 Testing Secret Key with simpler endpoint...');

        // Try a simpler endpoint first
        const response = await axios.get(
            'https://api.sandbox.checkout.com/workflows',
            {
                headers: {
                    'Authorization': `Bearer ${secretKey}`,
                    'Content-Type': 'application/json'
                }
            }
        );

        console.log('✅ Secret Key is working! (Basic auth test passed)');
        console.log('📄 Response status:', response.status);

        // Now try payment session
        console.log('\n🧪 Testing Payment Session creation...');

        const sessionResponse = await axios.post(
            'https://api.sandbox.checkout.com/payment-sessions',
            {
                amount: 1000,
                currency: 'USD', // Use USD instead of PHP to avoid processing channel issues
                reference: 'TEST-' + Date.now(),
                display_name: 'API Key Test',
                payment_type: 'Regular',
                customer: {
                    name: 'Test Customer',
                    email: '<EMAIL>'
                },
                items: [{
                    reference: 'test-item',
                    name: 'Test Item',
                    quantity: 1,
                    unit_price: 1000
                }],
                success_url: 'http://localhost:3000/success',
                failure_url: 'http://localhost:3000/failure'
            },
            {
                headers: {
                    'Authorization': `Bearer ${secretKey}`,
                    'Content-Type': 'application/json'
                }
            }
        );

        console.log('✅ Payment Session created successfully!');
        console.log('📄 Payment Session ID:', sessionResponse.data.id);
        console.log('🔑 Public Key:', publicKey);
        
    } catch (error) {
        console.error('❌ Secret Key Error:');
        if (error.response) {
            console.error('Status:', error.response.status);
            console.error('Message:', error.response.data);
            
            if (error.response.status === 401) {
                console.error('\n🔍 401 Unauthorized - Possible causes:');
                console.error('1. Invalid secret key');
                console.error('2. Key doesn\'t have "payment-sessions" scope');
                console.error('3. Key is for wrong environment (production vs sandbox)');
            }
        } else {
            console.error('Network Error:', error.message);
        }
    }
}

testAPIKeys();
