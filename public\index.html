<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GCash Payment Demo - Checkout.com</title>
    <link rel="stylesheet" href="styles.css">
    <script src="https://checkout-web-components.checkout.com/index.js"></script>
</head>
<body>
    <div class="container">
        <header>
            <h1>🏪 GCash Payment Demo</h1>
            <p>Secure payments powered by Checkout.com</p>
        </header>

        <div class="payment-form">
            <h2>Order Details</h2>
            
            <form id="orderForm">
                <div class="form-group">
                    <label for="customerName">Customer Name:</label>
                    <input type="text" id="customerName" name="customerName" required 
                           placeholder="Enter your full name">
                </div>

                <div class="form-group">
                    <label for="customerEmail">Email Address:</label>
                    <input type="email" id="customerEmail" name="customerEmail" required 
                           placeholder="<EMAIL>">
                </div>

                <div class="form-group">
                    <label for="currency">Currency:</label>
                    <select id="currency" name="currency">
                        <option value="PHP">PHP - Philippine Peso</option>
                        <option value="HKD">HKD - Hong Kong Dollar</option>
                        <option value="SGD">SGD - Singapore Dollar</option>
                    </select>
                </div>

                <div class="items-section">
                    <h3>Items</h3>
                    <div id="itemsList">
                        <div class="item-row">
                            <input type="text" placeholder="Item name" class="item-name" value="Sample Product">
                            <input type="number" placeholder="Qty" class="item-quantity" value="1" min="1">
                            <input type="number" placeholder="Price" class="item-price" value="100.00" step="0.01" min="0">
                            <button type="button" class="remove-item">Remove</button>
                        </div>
                    </div>
                    <button type="button" id="addItem">Add Item</button>
                </div>

                <div class="total-section">
                    <h3>Total: <span id="totalAmount">₱100.00</span></h3>
                </div>

                <div class="payment-method-section">
                    <h3>Choose Payment Method</h3>
                    <div class="payment-methods">
                        <label class="payment-method-option">
                            <input type="radio" name="paymentMethod" value="gcash" checked>
                            <span class="payment-method-label">
                                <strong>GCash</strong>
                                <small>Pay with your GCash wallet</small>
                            </span>
                        </label>
                        <label class="payment-method-option">
                            <input type="radio" name="paymentMethod" value="card">
                            <span class="payment-method-label">
                                <strong>Credit/Debit Card</strong>
                                <small>Pay with Visa, Mastercard, etc.</small>
                            </span>
                        </label>
                    </div>
                </div>

                <button type="submit" id="createPayment" class="pay-button">
                    Create Payment
                </button>
            </form>
        </div>

        <div id="paymentSection" class="payment-section" style="display: none;">
            <h2>Pay with GCash</h2>
            <div id="gcash-container"></div>
            <div id="paymentStatus"></div>
        </div>

        <div class="info-section">
            <h3>ℹ️ Test Information</h3>
            <ul>
                <li>This is a <strong>sandbox environment</strong> for testing</li>
                <li>No real money will be charged</li>
                <li>Use test credentials for GCash payments</li>
                <li>Check the browser console for detailed logs</li>
            </ul>
        </div>
    </div>

    <script src="app.js"></script>
</body>
</html>
