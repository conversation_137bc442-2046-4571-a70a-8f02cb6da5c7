// Configuration file for GCash Checkout.com integration
require('dotenv').config();

const config = {
  // Checkout.com Configuration
  checkout: {
    secretKey: process.env.CHECKOUT_SECRET_KEY,
    publicKey: process.env.CHECKOUT_PUBLIC_KEY,
    environment: process.env.CHECKOUT_ENVIRONMENT || 'sandbox',
    apiUrl: process.env.CHECKOUT_ENVIRONMENT === 'production' 
      ? 'https://api.checkout.com' 
      : 'https://api.sandbox.checkout.com'
  },

  // Server Configuration
  server: {
    port: process.env.PORT || 3000,
    nodeEnv: process.env.NODE_ENV || 'development'
  },

  // Webhook Configuration
  webhook: {
    secret: process.env.WEBHOOK_SECRET,
    endpoint: '/api/webhooks/checkout'
  },

  // Application URLs
  urls: {
    success: process.env.SUCCESS_URL || `http://localhost:${process.env.PORT || 3000}/success`,
    failure: process.env.FAILURE_URL || `http://localhost:${process.env.PORT || 3000}/failure`
  },

  // GCash Configuration
  gcash: {
    supportedCurrencies: ['PHP', 'HKD', 'SGD'],
    requiredCountry: 'PH',
    paymentTypes: ['Regular', 'Recurring']
  },

  // Security Configuration
  security: {
    corsOrigins: process.env.NODE_ENV === 'production' 
      ? ['https://yourdomain.com'] 
      : ['http://localhost:3000', 'http://127.0.0.1:3000'],
    csp: {
      connectSrc: ["'self'", "https://*.checkout.com"],
      frameSrc: ["'self'", "https://*.checkout.com"],
      scriptSrc: ["'self'", "https://*.checkout.com", "'unsafe-inline'"],
      imgSrc: ["'self'", "https://*.checkout.com", "data:"]
    }
  }
};

// Validation
function validateConfig() {
  const errors = [];

  if (!config.checkout.secretKey) {
    errors.push('CHECKOUT_SECRET_KEY is required');
  }

  if (!config.checkout.publicKey) {
    errors.push('CHECKOUT_PUBLIC_KEY is required');
  }

  if (!['sandbox', 'production'].includes(config.checkout.environment)) {
    errors.push('CHECKOUT_ENVIRONMENT must be either "sandbox" or "production"');
  }

  if (errors.length > 0) {
    console.error('Configuration errors:');
    errors.forEach(error => console.error(`- ${error}`));
    process.exit(1);
  }
}

// Validate configuration on load
validateConfig();

module.exports = config;
