const express = require('express');
const axios = require('axios');
const router = express.Router();

// Checkout.com API configuration
const CHECKOUT_API_URL = process.env.CHECKOUT_ENVIRONMENT === 'production' 
  ? 'https://api.checkout.com' 
  : 'https://api.sandbox.checkout.com';

const checkoutHeaders = {
  'Authorization': `Bearer ${process.env.CHECKOUT_SECRET_KEY}`,
  'Content-Type': 'application/json'
};

// Create payment session for GCash
router.post('/create-session', async (req, res) => {
  try {
    const { amount, currency = 'PHP', reference, customerName, customerEmail, items } = req.body;

    // Validate required fields
    if (!amount || !reference || !customerEmail || !items || !Array.isArray(items)) {
      return res.status(400).json({
        error: 'Missing required fields',
        required: ['amount', 'reference', 'customerEmail', 'items']
      });
    }

    // Validate currency for GCash
    const allowedCurrencies = ['PHP', 'HKD', 'SGD'];
    if (!allowedCurrencies.includes(currency)) {
      return res.status(400).json({
        error: 'Invalid currency for GCash',
        allowed: allowedCurrencies
      });
    }

    // Validate items structure
    for (const item of items) {
      if (!item.name || !item.quantity || !item.reference || !item.unit_price) {
        return res.status(400).json({
          error: 'Invalid item structure',
          required: ['name', 'quantity', 'reference', 'unit_price']
        });
      }
    }

    const paymentSessionData = {
      amount: Math.round(amount * 100), // Convert to cents
      currency: currency,
      reference: reference,
      display_name: "GCash Payment Demo",
      payment_type: "Regular",
      billing: {
        address: {
          country: "PH" // Required for GCash
        }
      },
      customer: {
        name: customerName || "Customer",
        email: customerEmail
      },
      items: items.map(item => ({
        reference: item.reference,
        name: item.name,
        quantity: parseInt(item.quantity),
        unit_price: Math.round(item.unit_price * 100) // Convert to cents
      })),
      // Add payment methods configuration - start with cards, add GCash when processing channel is ready
      payment_methods: [
        {
          type: "card",
          card_schemes: ["Visa", "Mastercard", "American Express"]
        }
        // Uncomment when GCash processing channel is configured:
        // {
        //   type: "gcash"
        // }
      ],
      success_url: process.env.SUCCESS_URL || `http://localhost:${process.env.PORT || 3000}/success`,
      failure_url: process.env.FAILURE_URL || `http://localhost:${process.env.PORT || 3000}/failure`
    };

    console.log('Creating payment session:', JSON.stringify(paymentSessionData, null, 2));

    const response = await axios.post(
      `${CHECKOUT_API_URL}/payment-sessions`,
      paymentSessionData,
      { headers: checkoutHeaders }
    );

    console.log('Payment session created:', response.data.id);

    res.json({
      success: true,
      paymentSession: response.data,
      publicKey: process.env.CHECKOUT_PUBLIC_KEY
    });

  } catch (error) {
    console.error('Payment session creation error:', error.response?.data || error.message);
    
    res.status(500).json({
      error: 'Failed to create payment session',
      details: error.response?.data || error.message
    });
  }
});

// Get payment details
router.get('/payment/:paymentId', async (req, res) => {
  try {
    const { paymentId } = req.params;

    const response = await axios.get(
      `${CHECKOUT_API_URL}/payments/${paymentId}`,
      { headers: checkoutHeaders }
    );

    res.json({
      success: true,
      payment: response.data
    });

  } catch (error) {
    console.error('Get payment error:', error.response?.data || error.message);
    
    res.status(500).json({
      error: 'Failed to get payment details',
      details: error.response?.data || error.message
    });
  }
});

module.exports = router;
