const express = require('express');
const axios = require('axios');
const router = express.Router();

// Checkout.com API configuration
const CHECKOUT_API_URL = process.env.CHECKOUT_ENVIRONMENT === 'production' 
  ? 'https://api.checkout.com' 
  : 'https://api.sandbox.checkout.com';

const checkoutHeaders = {
  'Authorization': `Bearer ${process.env.CHECKOUT_SECRET_KEY}`,
  'Content-Type': 'application/json'
};

// Create payment with choice of GCash or Card
router.post('/create-payment', async (req, res) => {
  try {
    const { amount, currency = 'PHP', reference, customerName, customerEmail, items, paymentMethod } = req.body;

    // Validate required fields
    if (!amount || !reference || !customerEmail || !items || !Array.isArray(items) || !paymentMethod) {
      return res.status(400).json({
        error: 'Missing required fields',
        required: ['amount', 'reference', 'customerEmail', 'items', 'paymentMethod']
      });
    }

    // Validate payment method
    if (!['gcash', 'card'].includes(paymentMethod)) {
      return res.status(400).json({
        error: 'Invalid payment method. Must be "gcash" or "card"'
      });
    }

    // Validate currency for GCash
    const allowedCurrencies = ['PHP', 'HKD', 'SGD'];
    if (!allowedCurrencies.includes(currency)) {
      return res.status(400).json({
        error: 'Invalid currency for GCash',
        allowed: allowedCurrencies
      });
    }

    // Validate items structure
    for (const item of items) {
      if (!item.name || !item.quantity || !item.reference || !item.unit_price) {
        return res.status(400).json({
          error: 'Invalid item structure',
          required: ['name', 'quantity', 'reference', 'unit_price']
        });
      }
    }

    // Create payment data based on selected method
    let paymentData = {
      amount: Math.round(amount * 100), // Convert to cents
      currency: currency,
      success_url: process.env.SUCCESS_URL || `http://localhost:${process.env.PORT || 3000}/success`,
      failure_url: process.env.FAILURE_URL || `http://localhost:${process.env.PORT || 3000}/failure`,
      payment_type: "Regular",
      reference: reference,
      capture: true,
      customer: {
        name: customerName || "Customer",
        email: customerEmail
      },
      items: items.map(item => ({
        reference: item.reference,
        name: item.name,
        quantity: parseInt(item.quantity),
        unit_price: Math.round(item.unit_price * 100) // Convert to cents
      }))
    };

    // Configure source and processing based on payment method
    if (paymentMethod === 'gcash') {
      paymentData.source = {
        type: "gcash"
      };
      paymentData.processing = {
        terminal_type: "WEB" // WEB for browser-based payments
      };
      paymentData.shipping = {
        address: {
          country: "PH" // Required for GCash
        }
      };
    } else if (paymentMethod === 'card') {
      // For card payments, we'll use payment sessions instead of direct payments
      // This approach works better for card payments
      return await createCardPaymentSession(req, res);
    }

    console.log(`Creating ${paymentMethod} payment:`, JSON.stringify(paymentData, null, 2));

    const response = await axios.post(
      `${CHECKOUT_API_URL}/payments`,
      paymentData,
      { headers: checkoutHeaders }
    );

    console.log(`${paymentMethod} payment created:`, response.data.id);

    res.json({
      success: true,
      payment: response.data,
      redirectUrl: response.data._links?.redirect?.href,
      paymentId: response.data.id,
      status: response.data.status,
      paymentMethod: paymentMethod
    });

  } catch (error) {
    const { paymentMethod } = req.body;
    console.error(`${paymentMethod || 'payment'} creation error:`, error.response?.data || error.message);

    res.status(500).json({
      error: `Failed to create ${paymentMethod || 'payment'}`,
      details: error.response?.data || error.message
    });
  }
});

// Create card payment session (fallback for when processing channel is not set up)
async function createCardPaymentSession(req, res) {
  try {
    const { amount, currency, reference, customerName, customerEmail, items } = req.body;

    const paymentSessionData = {
      amount: Math.round(amount * 100), // Convert to cents
      currency: currency,
      reference: reference,
      display_name: "Card Payment Demo",
      payment_type: "Regular",
      customer: {
        name: customerName || "Customer",
        email: customerEmail
      },
      items: items.map(item => ({
        reference: item.reference,
        name: item.name,
        quantity: parseInt(item.quantity),
        unit_price: Math.round(item.unit_price * 100) // Convert to cents
      })),
      payment_methods: [
        {
          type: "card",
          card_schemes: ["Visa", "Mastercard", "American Express"]
        }
      ],
      success_url: process.env.SUCCESS_URL || `http://localhost:${process.env.PORT || 3000}/success`,
      failure_url: process.env.FAILURE_URL || `http://localhost:${process.env.PORT || 3000}/failure`
    };

    console.log('Creating card payment session:', JSON.stringify(paymentSessionData, null, 2));

    const response = await axios.post(
      `${CHECKOUT_API_URL}/payment-sessions`,
      paymentSessionData,
      { headers: checkoutHeaders }
    );

    console.log('Card payment session created:', response.data.id);

    res.json({
      success: true,
      paymentSession: response.data,
      publicKey: process.env.CHECKOUT_PUBLIC_KEY,
      paymentMethod: 'card',
      useFlow: true // Indicates frontend should use Checkout Flow
    });

  } catch (error) {
    console.error('Card payment session creation error:', error.response?.data || error.message);

    res.status(500).json({
      error: 'Failed to create card payment session',
      details: error.response?.data || error.message
    });
  }
}

// Get payment details
router.get('/payment/:paymentId', async (req, res) => {
  try {
    const { paymentId } = req.params;

    const response = await axios.get(
      `${CHECKOUT_API_URL}/payments/${paymentId}`,
      { headers: checkoutHeaders }
    );

    res.json({
      success: true,
      payment: response.data
    });

  } catch (error) {
    console.error('Get payment error:', error.response?.data || error.message);
    
    res.status(500).json({
      error: 'Failed to get payment details',
      details: error.response?.data || error.message
    });
  }
});

module.exports = router;
