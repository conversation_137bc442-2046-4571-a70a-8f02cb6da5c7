<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Payment Failed - GCash Demo</title>
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <div class="container">
        <header style="background: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%);">
            <h1>❌ Payment Failed</h1>
            <p>There was an issue processing your GCash payment</p>
        </header>

        <div class="payment-form">
            <div class="failure-content">
                <div class="failure-icon">😞</div>
                <h2>Payment Could Not Be Processed</h2>
                <p>We're sorry, but your payment could not be completed at this time.</p>
                
                <div id="paymentDetails" class="payment-details">
                    <h3>Payment Information</h3>
                    <div class="detail-row">
                        <span class="label">Payment ID:</span>
                        <span class="value" id="paymentId">Loading...</span>
                    </div>
                    <div class="detail-row">
                        <span class="label">Status:</span>
                        <span class="value status-error">Failed</span>
                    </div>
                    <div class="detail-row">
                        <span class="label">Reason:</span>
                        <span class="value" id="failureReason">Loading...</span>
                    </div>
                    <div class="detail-row">
                        <span class="label">Amount:</span>
                        <span class="value" id="amount">Loading...</span>
                    </div>
                    <div class="detail-row">
                        <span class="label">Reference:</span>
                        <span class="value" id="reference">Loading...</span>
                    </div>
                    <div class="detail-row">
                        <span class="label">Date:</span>
                        <span class="value" id="processedOn">Loading...</span>
                    </div>
                </div>

                <div class="troubleshooting">
                    <h3>💡 What You Can Do</h3>
                    <ul>
                        <li>Check your GCash account balance and ensure you have sufficient funds</li>
                        <li>Verify your GCash account is active and not suspended</li>
                        <li>Try using a different payment method</li>
                        <li>Contact your bank or GCash support if the issue persists</li>
                        <li>Wait a few minutes and try again</li>
                    </ul>
                </div>

                <div class="actions">
                    <a href="/" class="pay-button">Try Again</a>
                    <button onclick="contactSupport()" class="secondary-button">Contact Support</button>
                </div>
            </div>
        </div>

        <div class="info-section" style="background: #f8d7da; border-top: 2px solid #f5c6cb;">
            <h3 style="color: #721c24;">🔒 Your Information is Safe</h3>
            <ul style="color: #721c24;">
                <li>No charges have been made to your account</li>
                <li>Your payment information remains secure</li>
                <li>You can safely retry the payment</li>
                <li>All transactions are encrypted and protected</li>
            </ul>
        </div>
    </div>

    <script>
        // Extract payment ID from URL parameters
        const urlParams = new URLSearchParams(window.location.search);
        const paymentId = urlParams.get('cko-payment-id');

        if (paymentId) {
            document.getElementById('paymentId').textContent = paymentId;
            
            // Fetch payment details
            fetchPaymentDetails(paymentId);
        } else {
            document.getElementById('paymentDetails').innerHTML = 
                '<p class="status-error">No payment ID found in URL</p>';
        }

        async function fetchPaymentDetails(paymentId) {
            try {
                const response = await fetch(`/api/payments/payment/${paymentId}`);
                const data = await response.json();

                if (data.success && data.payment) {
                    const payment = data.payment;
                    
                    // Update payment details
                    document.getElementById('amount').textContent = 
                        (payment.amount / 100).toFixed(2);
                    document.getElementById('reference').textContent = 
                        payment.reference || 'N/A';
                    document.getElementById('processedOn').textContent = 
                        new Date(payment.processed_on || Date.now()).toLocaleString();
                    
                    // Show failure reason
                    const failureReason = payment.response_summary || 
                                        payment.response_code || 
                                        'Payment declined';
                    document.getElementById('failureReason').textContent = failureReason;

                    console.log('Payment details loaded:', payment);
                } else {
                    throw new Error('Failed to load payment details');
                }
            } catch (error) {
                console.error('Error fetching payment details:', error);
                document.getElementById('paymentDetails').innerHTML = 
                    '<p class="status-error">Unable to load payment details</p>';
            }
        }

        function contactSupport() {
            // In a real application, this would open a support ticket or chat
            alert('Support contact feature would be implemented here.\n\nFor now, please email: <EMAIL>');
        }
    </script>

    <style>
        .failure-content {
            text-align: center;
            padding: 40px 20px;
        }

        .failure-icon {
            font-size: 4rem;
            margin-bottom: 20px;
        }

        .failure-content h2 {
            color: #dc3545;
            margin-bottom: 15px;
            font-size: 2rem;
        }

        .failure-content p {
            color: #666;
            font-size: 1.1rem;
            margin-bottom: 30px;
        }

        .payment-details {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 25px;
            margin: 30px 0;
            text-align: left;
        }

        .payment-details h3 {
            margin-bottom: 20px;
            color: #333;
            text-align: center;
        }

        .detail-row {
            display: flex;
            justify-content: space-between;
            padding: 10px 0;
            border-bottom: 1px solid #e9ecef;
        }

        .detail-row:last-child {
            border-bottom: none;
        }

        .label {
            font-weight: 600;
            color: #495057;
        }

        .value {
            color: #212529;
            font-family: monospace;
        }

        .status-error {
            color: #dc3545 !important;
            font-weight: bold;
        }

        .troubleshooting {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 8px;
            padding: 20px;
            margin: 30px 0;
            text-align: left;
        }

        .troubleshooting h3 {
            color: #856404;
            margin-bottom: 15px;
            text-align: center;
        }

        .troubleshooting ul {
            color: #856404;
            list-style-position: inside;
        }

        .troubleshooting li {
            margin-bottom: 8px;
        }

        .actions {
            display: flex;
            gap: 15px;
            justify-content: center;
            margin-top: 30px;
        }

        .secondary-button {
            background: #6c757d;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            font-size: 16px;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
        }

        .secondary-button:hover {
            background: #5a6268;
        }

        @media (max-width: 768px) {
            .actions {
                flex-direction: column;
            }
            
            .detail-row {
                flex-direction: column;
                gap: 5px;
            }
        }
    </style>
</body>
</html>
