const express = require('express');
const axios = require('axios');
const router = express.Router();

// Configuration
const CHECKOUT_API_URL = process.env.CHECKOUT_ENVIRONMENT === 'production' 
  ? 'https://api.checkout.com' 
  : 'https://api.sandbox.checkout.com';

const checkoutHeaders = {
  'Authorization': `Bearer ${process.env.CHECKOUT_SECRET_KEY}`,
  'Content-Type': 'application/json'
};

// Create demo payment with cards (works without processing channel setup)
router.post('/create-demo-payment', async (req, res) => {
  try {
    const { amount, currency, customerName, customerEmail, items } = req.body;
    
    // Validate required fields
    if (!amount || !currency || !customerEmail || !items || items.length === 0) {
      return res.status(400).json({
        error: 'Missing required fields: amount, currency, customerEmail, items'
      });
    }

    // Generate unique reference
    const reference = `DEMO-${Date.now()}`;

    // Create simple payment request (no processing channel required)
    const paymentData = {
      amount: Math.round(amount * 100), // Convert to cents
      currency: currency === 'PHP' ? 'USD' : currency, // Use USD for demo
      source: {
        type: "card",
        number: "****************", // Test card
        expiry_month: 12,
        expiry_year: 2025,
        cvv: "123"
      },
      success_url: process.env.SUCCESS_URL || `http://localhost:${process.env.PORT || 3000}/success`,
      failure_url: process.env.FAILURE_URL || `http://localhost:${process.env.PORT || 3000}/failure`,
      payment_type: "Regular",
      reference: reference,
      capture: true,
      customer: {
        name: customerName || "Demo Customer",
        email: customerEmail
      },
      items: items.map(item => ({
        reference: item.reference,
        name: item.name,
        quantity: parseInt(item.quantity),
        unit_price: Math.round(item.unit_price * 100) // Convert to cents
      }))
    };

    console.log('Creating demo payment:', JSON.stringify(paymentData, null, 2));

    // Create payment
    const response = await axios.post(
      `${CHECKOUT_API_URL}/payments`,
      paymentData,
      { headers: checkoutHeaders }
    );

    console.log('Demo payment created:', response.data.id);

    res.json({
      success: true,
      payment: response.data,
      paymentId: response.data.id,
      status: response.data.status,
      message: "Demo payment successful! (Using test card instead of GCash)"
    });

  } catch (error) {
    console.error('Demo payment creation error:', error.response?.data || error.message);
    
    res.status(500).json({
      error: 'Failed to create demo payment',
      details: error.response?.data || error.message
    });
  }
});

module.exports = router;
