const express = require('express');
const router = express.Router();

// Webhook endpoint for Checkout.com events
router.post('/checkout', (req, res) => {
  try {
    const event = req.body;
    
    console.log('📨 Webhook received:', {
      type: event.type,
      id: event.id,
      timestamp: new Date().toISOString()
    });

    // Handle different webhook events
    switch (event.type) {
      case 'payment_approved':
        handlePaymentApproved(event);
        break;
      case 'payment_declined':
        handlePaymentDeclined(event);
        break;
      case 'payment_pending':
        handlePaymentPending(event);
        break;
      case 'payment_canceled':
        handlePaymentCanceled(event);
        break;
      default:
        console.log('🔔 Unhandled webhook event type:', event.type);
    }

    // Always respond with 200 to acknowledge receipt
    res.status(200).json({ received: true });

  } catch (error) {
    console.error('❌ Webhook processing error:', error);
    res.status(500).json({ error: 'Webhook processing failed' });
  }
});

function handlePaymentApproved(event) {
  console.log('✅ Payment Approved:', {
    paymentId: event.data.id,
    amount: event.data.amount,
    currency: event.data.currency,
    reference: event.data.reference,
    paymentSessionId: event.data.metadata?.cko_payment_session_id
  });

  // Here you would typically:
  // 1. Update your database with the successful payment
  // 2. Send confirmation email to customer
  // 3. Trigger order fulfillment process
  // 4. Update inventory
}

function handlePaymentDeclined(event) {
  console.log('❌ Payment Declined:', {
    paymentId: event.data.id,
    amount: event.data.amount,
    currency: event.data.currency,
    reference: event.data.reference,
    responseCode: event.data.response_code,
    responseSummary: event.data.response_summary
  });

  // Here you would typically:
  // 1. Log the declined payment
  // 2. Notify customer of payment failure
  // 3. Provide alternative payment options
}

function handlePaymentPending(event) {
  console.log('⏳ Payment Pending:', {
    paymentId: event.data.id,
    amount: event.data.amount,
    currency: event.data.currency,
    reference: event.data.reference
  });

  // Here you would typically:
  // 1. Update payment status to pending
  // 2. Set up monitoring for final status
  // 3. Notify customer that payment is being processed
}

function handlePaymentCanceled(event) {
  console.log('🚫 Payment Canceled:', {
    paymentId: event.data.id,
    amount: event.data.amount,
    currency: event.data.currency,
    reference: event.data.reference
  });

  // Here you would typically:
  // 1. Update payment status to canceled
  // 2. Release any reserved inventory
  // 3. Notify customer of cancellation
}

module.exports = router;
