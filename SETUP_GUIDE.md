# 🚀 Setup Guide - GCash Integration Requirements

## ✅ Current Status
Your API keys are **working correctly**! The issue is that GCash requires special setup in your Checkout.com account.

## 🔧 Required Steps for GCash

### ⚠️ Important: GCash Requires Account Manager Setup
According to Checkout.com documentation, **GCash must be activated by your account manager**. This is not something you can do yourself in the dashboard.

## 🔧 How to Fix This

### Step 1: Contact Checkout.com Support

**You MUST contact Checkout.com to enable GCash**. This cannot be done through the dashboard alone.

1. **Email Checkout.com Support**:
   ```
   Email: <EMAIL>
   Subject: Enable GCash for Sandbox Account
   ```

2. **Include in your email**:
   ```
   Hi Checkout.com Team,

   I need to enable GCash payments for my sandbox account for testing purposes.

   Account Email: [<EMAIL>]
   Business: [your business name]
   Use Case: Testing GCash integration for Philippines market

   Please:
   1. Activate GCash payments in my sandbox environment
   2. Configure the required processing channel
   3. Provide any additional setup instructions

   Thank you!
   ```

3. **Wait for Account Manager Response**:
   - They will configure GCash on their end
   - You'll receive setup instructions
   - Processing channel will be created automatically

### Step 2: Verify Configuration

After setting up the processing channel:

1. **Test the API keys again**:
   ```bash
   node test-api-keys.js
   ```

2. **Should see**:
   ```
   ✅ Secret Key is working!
   ✅ Payment Session created successfully!
   📄 Payment Session ID: ps_xxxxx
   ```

### Step 3: Enable GCash

Once the basic processing channel works:

1. **Update the payment methods** in `routes/payments.js`:
   ```javascript
   payment_methods: [
     {
       type: "card",
       card_schemes: ["Visa", "Mastercard", "American Express"]
     },
     {
       type: "gcash"  // Uncomment this line
     }
   ]
   ```

2. **Update the frontend** in `public/app.js` to use GCash component

## 🎯 What Each Error Means

### ✅ `200 OK` - API Keys Working
Your authentication is correct, keys have proper permissions.

### ❌ `422 processing_channel_id_required`
You need to configure a processing channel in the dashboard.

### ❌ `401 Unauthorized`
API keys are invalid or don't have required scopes.

## 🔍 Troubleshooting

### If Processing Channel Setup Doesn't Work:

1. **Contact Checkout.com Support**:
   - They can help configure GCash processing
   - Mention you're setting up GCash for Philippines

2. **Check Account Status**:
   - Ensure your sandbox account is fully activated
   - Some features require account verification

3. **Try Different Currencies**:
   - Start with USD/EUR for basic testing
   - Add PHP/GCash once processing is configured

## 📞 Getting Help

### Checkout.com Support:
- **Email**: <EMAIL>
- **Documentation**: https://www.checkout.com/docs
- **Dashboard**: https://dashboard.sandbox.checkout.com

### What to Tell Support:
```
Hi, I'm setting up GCash payments in the sandbox environment.
I'm getting "processing_channel_id_required" error when creating payment sessions.
My account email: [your-email]
Error: Need help configuring GCash processing channel for Philippines market.
```

## 🚀 Once Fixed

After processing channel is configured:

1. **Restart the server**:
   ```bash
   npm start
   ```

2. **Test the payment flow**:
   - Go to http://localhost:3000
   - Fill in the form
   - Create payment session
   - Complete GCash payment

3. **Check webhooks**:
   - Monitor server logs for webhook events
   - Verify payment status updates

## 📋 Checklist

- [ ] Log in to Checkout.com dashboard
- [ ] Navigate to Settings → Processing Channels  
- [ ] Add processing channel for cards
- [ ] Add processing channel for GCash
- [ ] Save configuration
- [ ] Test API keys again
- [ ] Update code to enable GCash
- [ ] Test full payment flow
- [ ] Configure webhooks for production

---

**Note**: This is a common setup step that all Checkout.com integrations require. Once configured, your GCash integration will work perfectly!
