# 🚀 Setup Guide - Fixing the Processing Channel Issue

## ✅ Current Status
Your API keys are **working correctly**! The issue is that Checkout.com requires a **processing channel** to be configured for payment sessions.

## 🔧 How to Fix This

### Step 1: Configure Processing Channel in Dashboard

1. **Log in** to your Checkout.com Sandbox Dashboard:
   ```
   https://dashboard.sandbox.checkout.com
   ```

2. **Navigate to Settings**:
   - Click on "Settings" in the left sidebar
   - Select "Processing Channels"

3. **Add a Processing Channel**:
   - Click "Add Processing Channel"
   - Choose your processor (usually "Checkout.com" for sandbox)
   - Configure for your region (Philippines for GCash)

4. **Enable Payment Methods**:
   - ✅ **Cards**: Visa, Mastercard, American Express
   - ✅ **GCash**: For Philippines market
   - ✅ **Other APMs**: As needed

5. **Save Configuration**:
   - Click "Save" to activate the processing channel

### Step 2: Verify Configuration

After setting up the processing channel:

1. **Test the API keys again**:
   ```bash
   node test-api-keys.js
   ```

2. **Should see**:
   ```
   ✅ Secret Key is working!
   ✅ Payment Session created successfully!
   📄 Payment Session ID: ps_xxxxx
   ```

### Step 3: Enable GCash

Once the basic processing channel works:

1. **Update the payment methods** in `routes/payments.js`:
   ```javascript
   payment_methods: [
     {
       type: "card",
       card_schemes: ["Visa", "Mastercard", "American Express"]
     },
     {
       type: "gcash"  // Uncomment this line
     }
   ]
   ```

2. **Update the frontend** in `public/app.js` to use GCash component

## 🎯 What Each Error Means

### ✅ `200 OK` - API Keys Working
Your authentication is correct, keys have proper permissions.

### ❌ `422 processing_channel_id_required`
You need to configure a processing channel in the dashboard.

### ❌ `401 Unauthorized`
API keys are invalid or don't have required scopes.

## 🔍 Troubleshooting

### If Processing Channel Setup Doesn't Work:

1. **Contact Checkout.com Support**:
   - They can help configure GCash processing
   - Mention you're setting up GCash for Philippines

2. **Check Account Status**:
   - Ensure your sandbox account is fully activated
   - Some features require account verification

3. **Try Different Currencies**:
   - Start with USD/EUR for basic testing
   - Add PHP/GCash once processing is configured

## 📞 Getting Help

### Checkout.com Support:
- **Email**: <EMAIL>
- **Documentation**: https://www.checkout.com/docs
- **Dashboard**: https://dashboard.sandbox.checkout.com

### What to Tell Support:
```
Hi, I'm setting up GCash payments in the sandbox environment.
I'm getting "processing_channel_id_required" error when creating payment sessions.
My account email: [your-email]
Error: Need help configuring GCash processing channel for Philippines market.
```

## 🚀 Once Fixed

After processing channel is configured:

1. **Restart the server**:
   ```bash
   npm start
   ```

2. **Test the payment flow**:
   - Go to http://localhost:3000
   - Fill in the form
   - Create payment session
   - Complete GCash payment

3. **Check webhooks**:
   - Monitor server logs for webhook events
   - Verify payment status updates

## 📋 Checklist

- [ ] Log in to Checkout.com dashboard
- [ ] Navigate to Settings → Processing Channels  
- [ ] Add processing channel for cards
- [ ] Add processing channel for GCash
- [ ] Save configuration
- [ ] Test API keys again
- [ ] Update code to enable GCash
- [ ] Test full payment flow
- [ ] Configure webhooks for production

---

**Note**: This is a common setup step that all Checkout.com integrations require. Once configured, your GCash integration will work perfectly!
